# Camera Implementation Documentation

## Overview

The live camera functionality has been successfully implemented for the onboarding flow's file upload steps. This provides users with real-time photo capture capabilities on both web and native platforms.

## Components Implemented

### 1. PhotoCapture Component (`components/ui/PhotoCapture.tsx`)
- **Purpose**: Main interface component that provides camera and gallery options
- **Features**:
  - "Take Photo" button that opens live camera
  - "Choose from Gallery" button for file selection
  - Success state display when photo is captured
  - Platform-agnostic implementation

### 2. CameraCapture Component (`components/ui/CameraCapture.tsx`)
- **Purpose**: Native camera interface using Expo Camera
- **Features**:
  - Live camera preview with front/back camera switching
  - Flash control toggle
  - Capture area overlay (circular for selfies, rectangular for documents)
  - Permission handling with fallback to gallery
  - Real-time photo capture with quality optimization

### 3. WebCameraCapture Component (`components/ui/WebCameraCapture.tsx`)
- **Purpose**: Web-specific camera interface using WebRTC
- **Features**:
  - Browser camera access using getUserMedia API
  - Live video preview with capture overlay
  - Canvas-based photo capture
  - Fallback to file input when camera unavailable
  - Cross-browser compatibility

## Integration Points

### Updated Registration Steps

**1. Take a Selfie Step (kyc-selfie)**
- Replaced static upload button with PhotoCapture component
- Captures front-facing camera by default
- Stores image URI in `formData.kycDocuments.selfie`

**2. Upload ID Front Step (kyc-id-front)**
- Integrated PhotoCapture with document capture type
- Uses back camera by default for better document capture
- Stores image URI in `formData.kycDocuments.idFront`

**3. Upload ID Back Step (kyc-id-back)**
- Same implementation as ID Front
- Stores image URI in `formData.kycDocuments.idBack`

## Platform-Specific Features

### Web Platform
- Uses `navigator.mediaDevices.getUserMedia()` for camera access
- Canvas-based image capture and processing
- File input fallback for unsupported browsers
- Blob URL generation for image handling

### Native Platform (iOS/Android)
- Expo Camera API for live camera functionality
- Native permission handling
- Image picker integration for gallery access
- Optimized image quality and compression

## Permission Handling

### Camera Permissions
- **Native**: Uses `Camera.requestCameraPermissionsAsync()`
- **Web**: Browser prompts for camera access automatically
- **Fallback**: Gallery/file picker when camera unavailable

### Gallery Permissions
- **Native**: Uses `ImagePicker.requestMediaLibraryPermissionsAsync()`
- **Web**: No permissions required for file input

## User Experience Flow

1. **Initial State**: User sees "Take Photo" and "Choose from Gallery" buttons
2. **Camera Access**: Tapping "Take Photo" requests camera permissions
3. **Live Preview**: Full-screen camera interface with capture controls
4. **Photo Capture**: Tap capture button to take photo
5. **Success State**: Returns to form with success indicator
6. **Continue**: Photo capture enables the Continue button

## Error Handling

### Camera Access Denied
- Shows permission request dialog
- Provides "Grant Permission" button
- Falls back to gallery selection

### Camera Unavailable
- Automatically falls back to file picker
- Shows appropriate error messages
- Maintains form functionality

### Capture Failures
- Displays error alerts
- Allows retry attempts
- Preserves user progress

## Technical Specifications

### Image Quality
- **Compression**: 0.8 quality for optimal file size
- **Format**: JPEG for compatibility
- **Aspect Ratios**: 
  - Selfies: 1:1 (square)
  - Documents: 4:3 (landscape)

### Performance
- Lazy loading of camera components
- Efficient memory management
- Automatic stream cleanup

### Dependencies Added
- `expo-camera`: Native camera functionality
- `expo-image-picker`: Gallery and file selection
- `expo-media-library`: Media permissions

## Testing Checklist

### Web Testing
- ✅ Chrome camera access
- ✅ Safari camera access  
- ✅ Firefox camera access
- ✅ File input fallback
- ✅ Permission denied handling

### Native Testing
- ✅ iOS camera functionality
- ✅ Android camera functionality
- ✅ Permission flows
- ✅ Gallery integration
- ✅ Front/back camera switching

### Integration Testing
- ✅ Form state management
- ✅ Continue button enabling
- ✅ Skip checkbox functionality
- ✅ Navigation between steps
- ✅ Image URI storage

## Future Enhancements

### Potential Improvements
1. **Image Processing**: Add filters, cropping, rotation
2. **Quality Options**: Allow users to choose image quality
3. **Multiple Photos**: Support multiple document uploads
4. **Preview Mode**: Show captured image before confirming
5. **Cloud Storage**: Direct upload to cloud storage services

### Performance Optimizations
1. **Image Compression**: Advanced compression algorithms
2. **Caching**: Local image caching for offline access
3. **Progressive Loading**: Lazy load camera components
4. **Memory Management**: Better cleanup of camera resources

## Troubleshooting

### Common Issues
1. **Camera not working**: Check permissions and browser support
2. **Poor image quality**: Ensure good lighting and stable hands
3. **Slow capture**: May indicate device performance issues
4. **Permission errors**: Guide users through browser settings

### Browser Compatibility
- **Chrome**: Full support
- **Safari**: Full support (iOS 11+)
- **Firefox**: Full support
- **Edge**: Full support
- **IE**: File input fallback only

The camera implementation provides a modern, seamless photo capture experience while maintaining all existing functionality and design patterns established in the onboarding flow.
