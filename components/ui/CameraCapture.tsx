import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Platform, Alert, Modal } from 'react-native';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';

interface CameraCaptureProps {
  isVisible: boolean;
  onClose: () => void;
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
}

export const CameraCapture: React.FC<CameraCaptureProps> = ({
  isVisible,
  onClose,
  onCapture,
  captureType,
  title,
  subtitle
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState<CameraType>(
    captureType === 'selfie' ? CameraType.front : CameraType.back
  );
  const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.off);
  const [isLoading, setIsLoading] = useState(false);
  const cameraRef = useRef<Camera>(null);

  useEffect(() => {
    if (isVisible) {
      requestPermissions();
    }
  }, [isVisible]);

  const requestPermissions = async () => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera access to take photos.',
          [
            { text: 'Cancel', onPress: onClose },
            { text: 'Settings', onPress: () => ImagePicker.requestCameraPermissionsAsync() }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      setHasPermission(false);
    }
  };

  const takePicture = async () => {
    if (!cameraRef.current || isLoading) return;

    try {
      setIsLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        onCapture(photo.uri);
        onClose();
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const pickFromGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please enable photo library access.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: captureType === 'selfie' ? [1, 1] : [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onCapture(result.assets[0].uri);
        onClose();
      }
    } catch (error) {
      console.error('Error picking from gallery:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const toggleCameraType = () => {
    setCameraType(current => 
      current === CameraType.back ? CameraType.front : CameraType.back
    );
  };

  const toggleFlash = () => {
    setFlashMode(current => 
      current === FlashMode.off ? FlashMode.on : FlashMode.off
    );
  };

  if (!isVisible) return null;

  // Web fallback - use file input
  if (Platform.OS === 'web') {
    return (
      <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 bg-black">
          {/* Header */}
          <StyledView className="flex-row items-center justify-between p-4 bg-black/80">
            <StyledTouchableOpacity onPress={onClose} className="p-2">
              <Feather name="x" size={24} color="white" />
            </StyledTouchableOpacity>
            <StyledView className="flex-1 items-center">
              <StyledText className="text-white font-medium">{title}</StyledText>
            </StyledView>
            <StyledView className="w-10" />
          </StyledView>

          {/* Content */}
          <StyledView className="flex-1 items-center justify-center p-6">
            <StyledView className="bg-white rounded-xl p-8 w-full max-w-md">
              <StyledText className="text-xl font-bold text-center mb-2">{title}</StyledText>
              <StyledText className="text-gray-600 text-center mb-8">{subtitle}</StyledText>
              
              <StyledTouchableOpacity
                className="bg-secondary rounded-xl py-4 px-6 items-center mb-4"
                onPress={pickFromGallery}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="image" size={20} color="white" />
                  <StyledText className="text-white font-medium ml-2">Choose from Gallery</StyledText>
                </StyledView>
              </StyledTouchableOpacity>

              <StyledTouchableOpacity
                className="border border-gray-300 rounded-xl py-4 px-6 items-center"
                onPress={onClose}
              >
                <StyledText className="text-gray-700 font-medium">Cancel</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        </StyledView>
      </Modal>
    );
  }

  // Native camera interface
  if (hasPermission === null) {
    return (
      <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 items-center justify-center bg-black">
          <StyledText className="text-white">Requesting camera permission...</StyledText>
        </StyledView>
      </Modal>
    );
  }

  if (hasPermission === false) {
    return (
      <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 items-center justify-center bg-black p-6">
          <StyledText className="text-white text-center mb-4">
            Camera access is required to take photos
          </StyledText>
          <StyledTouchableOpacity
            className="bg-secondary rounded-xl py-3 px-6 mb-4"
            onPress={requestPermissions}
          >
            <StyledText className="text-white font-medium">Grant Permission</StyledText>
          </StyledTouchableOpacity>
          <StyledTouchableOpacity
            className="border border-white rounded-xl py-3 px-6"
            onPress={pickFromGallery}
          >
            <StyledText className="text-white font-medium">Choose from Gallery</StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      </Modal>
    );
  }

  return (
    <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
      <StyledView className="flex-1 bg-black">
        {/* Camera View */}
        <Camera
          ref={cameraRef}
          style={{ flex: 1 }}
          type={cameraType}
          flashMode={flashMode}
          ratio="4:3"
        >
          {/* Header Controls */}
          <StyledView className="flex-row items-center justify-between p-4 bg-black/50">
            <StyledTouchableOpacity onPress={onClose} className="p-2">
              <Feather name="x" size={24} color="white" />
            </StyledTouchableOpacity>
            
            <StyledView className="flex-1 items-center">
              <StyledText className="text-white font-medium">{title}</StyledText>
            </StyledView>

            <StyledTouchableOpacity onPress={toggleFlash} className="p-2">
              <Feather 
                name={flashMode === FlashMode.on ? "zap" : "zap-off"} 
                size={24} 
                color="white" 
              />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Capture Area Overlay */}
          <StyledView className="flex-1 items-center justify-center">
            {captureType === 'selfie' ? (
              <StyledView className="w-64 h-64 border-2 border-white/50 rounded-full" />
            ) : (
              <StyledView className="w-80 h-52 border-2 border-white/50 rounded-lg" />
            )}
          </StyledView>

          {/* Bottom Controls */}
          <StyledView className="flex-row items-center justify-between p-6 bg-black/50">
            <StyledTouchableOpacity onPress={pickFromGallery} className="p-3">
              <Feather name="image" size={24} color="white" />
            </StyledTouchableOpacity>

            <StyledTouchableOpacity
              onPress={takePicture}
              disabled={isLoading}
              className="w-20 h-20 bg-white rounded-full items-center justify-center"
            >
              {isLoading ? (
                <StyledView className="w-16 h-16 bg-gray-300 rounded-full" />
              ) : (
                <StyledView className="w-16 h-16 bg-white border-4 border-gray-300 rounded-full" />
              )}
            </StyledTouchableOpacity>

            <StyledTouchableOpacity onPress={toggleCameraType} className="p-3">
              <Feather name="rotate-cw" size={24} color="white" />
            </StyledTouchableOpacity>
          </StyledView>
        </Camera>
      </StyledView>
    </Modal>
  );
};
