import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Platform, Alert, Modal } from 'react-native';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';
import { PhotoPreview } from './PhotoPreview';

interface CameraCaptureProps {
  isVisible: boolean;
  onClose: () => void;
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
}

export const CameraCapture: React.FC<CameraCaptureProps> = ({
  isVisible,
  onClose,
  onCapture,
  captureType,
  title,
  subtitle
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState<CameraType>(
    captureType === 'selfie' ? CameraType.front : CameraType.back
  );
  const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.off);
  const [isLoading, setIsLoading] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const cameraRef = useRef<Camera>(null);

  useEffect(() => {
    if (isVisible) {
      requestPermissions();
    }
  }, [isVisible]);

  const requestPermissions = async () => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');

      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera access to take photos.',
          [
            { text: 'Cancel', onPress: onClose },
            { text: 'Settings', onPress: () => ImagePicker.requestCameraPermissionsAsync() }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      setHasPermission(false);
    }
  };

  const takePicture = async () => {
    if (!cameraRef.current || isLoading) return;

    try {
      setIsLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        setCapturedImage(photo.uri);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUsePhoto = () => {
    if (capturedImage) {
      onCapture(capturedImage);
      setShowPreview(false);
      onClose();
    }
  };

  const handleRetakePhoto = () => {
    setCapturedImage(null);
    setShowPreview(false);
  };

  const handleClose = () => {
    setCapturedImage(null);
    setShowPreview(false);
    onClose();
  };



  const toggleCameraType = () => {
    setCameraType(current =>
      current === CameraType.back ? CameraType.front : CameraType.back
    );
  };

  const toggleFlash = () => {
    setFlashMode(current =>
      current === FlashMode.off ? FlashMode.on : FlashMode.off
    );
  };

  if (!isVisible) return null;



  // Native camera interface
  if (hasPermission === null) {
    return (
      <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 items-center justify-center bg-black">
          <StyledText className="text-white">Requesting camera permission...</StyledText>
        </StyledView>
      </Modal>
    );
  }

  if (hasPermission === false) {
    return (
      <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 items-center justify-center bg-black p-6">
          <Feather name="camera-off" size={64} color="white" style={{ marginBottom: 24 }} />
          <StyledText className="text-white text-center mb-4 text-xl font-medium">
            Camera Access Required
          </StyledText>
          <StyledText className="text-white/70 text-center mb-8 text-base">
            This feature requires camera access to take live photos. Please allow camera access and try again.
          </StyledText>
          <StyledTouchableOpacity
            className="bg-secondary rounded-xl py-4 px-8 mb-4"
            onPress={requestPermissions}
          >
            <StyledView className="flex-row items-center">
              <Feather name="camera" size={20} color="white" />
              <StyledText className="text-white font-medium ml-2">Grant Permission</StyledText>
            </StyledView>
          </StyledTouchableOpacity>
          <StyledTouchableOpacity
            className="border border-white/30 rounded-xl py-4 px-8"
            onPress={onClose}
          >
            <StyledText className="text-white/70 font-medium">Cancel</StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      </Modal>
    );
  }

  return (
    <>
      <Modal visible={isVisible && !showPreview} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 bg-black">
          {/* Camera View */}
          <Camera
            ref={cameraRef}
            style={{ flex: 1 }}
            type={cameraType}
            flashMode={flashMode}
            ratio="4:3"
          >
            {/* Header Controls */}
            <StyledView className="flex-row items-center justify-between p-4 bg-black/50">
              <StyledTouchableOpacity onPress={handleClose} className="p-2">
                <Feather name="x" size={24} color="white" />
              </StyledTouchableOpacity>

            <StyledView className="flex-1 items-center">
              <StyledText className="text-white font-medium">{title}</StyledText>
            </StyledView>

            <StyledTouchableOpacity onPress={toggleFlash} className="p-2">
              <Feather
                name={flashMode === FlashMode.on ? "zap" : "zap-off"}
                size={24}
                color="white"
              />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Capture Area Overlay */}
          <StyledView className="flex-1 items-center justify-center">
            {captureType === 'selfie' ? (
              <StyledView className="w-64 h-64 border-2 border-white/50 rounded-full" />
            ) : (
              <StyledView
                className="border-2 border-white/50 rounded-lg"
                style={{ width: 600, height: 375 }}
              />
            )}
          </StyledView>

          {/* Instructions */}
          <StyledView className="absolute bottom-32 left-0 right-0 items-center">
            <StyledView className="bg-black/70 rounded-lg px-4 py-2 mx-4">
              <StyledText className="text-white text-center text-sm">
                {captureType === 'selfie'
                  ? 'Position your face in the circle'
                  : 'Fill the frame with your document for best quality'
                }
              </StyledText>
            </StyledView>
          </StyledView>

          {/* Bottom Controls */}
          <StyledView className="flex-row items-center justify-between p-6 bg-black/50">
            <StyledView className="w-12" />

            <StyledTouchableOpacity
              onPress={takePicture}
              disabled={isLoading}
              className="w-20 h-20 bg-white rounded-full items-center justify-center shadow-lg"
            >
              {isLoading ? (
                <StyledView className="w-16 h-16 bg-gray-300 rounded-full" />
              ) : (
                <StyledView className="w-16 h-16 bg-white border-4 border-gray-300 rounded-full" />
              )}
            </StyledTouchableOpacity>

            <StyledTouchableOpacity onPress={toggleCameraType} className="p-3">
              <Feather name="rotate-cw" size={24} color="white" />
            </StyledTouchableOpacity>
          </StyledView>
        </Camera>
      </StyledView>
      </Modal>

      {/* Photo Preview Modal */}
      <PhotoPreview
        isVisible={showPreview}
        imageUri={capturedImage || ''}
        captureType={captureType}
        title={title}
        onUsePhoto={handleUsePhoto}
        onRetake={handleRetakePhoto}
        onClose={handleClose}
      />
    </>
  );
};
