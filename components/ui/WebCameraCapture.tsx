import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';

interface WebCameraCaptureProps {
  isVisible: boolean;
  onClose: () => void;
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
}

export const WebCameraCapture: React.FC<WebCameraCaptureProps> = ({
  isVisible,
  onClose,
  onCapture,
  captureType,
  title,
  subtitle
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (isVisible && typeof navigator !== 'undefined' && navigator.mediaDevices) {
      startCamera();
    }

    return () => {
      stopCamera();
    };
  }, [isVisible]);

  const startCamera = async () => {
    try {
      // Enhanced constraints for better macOS compatibility
      const constraints = {
        video: {
          facingMode: captureType === 'selfie' ? 'user' : 'environment',
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          frameRate: { ideal: 30, min: 15 },
          // Additional constraints for macOS Safari compatibility
          aspectRatio: { ideal: 16/9 },
          resizeMode: 'crop-and-scale'
        },
        audio: false
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      setStream(mediaStream);
      setHasPermission(true);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        // Enhanced video setup for macOS compatibility
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play().catch(console.error);
        };

        // Ensure video plays immediately if metadata is already loaded
        if (videoRef.current.readyState >= 1) {
          videoRef.current.play().catch(console.error);
        }
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setHasPermission(false);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || isLoading) return;

    try {
      setIsLoading(true);
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) return;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to blob and create URL
      canvas.toBlob((blob) => {
        if (blob) {
          const imageUrl = URL.createObjectURL(blob);
          onCapture(imageUrl);
          onClose();
        }
      }, 'image/jpeg', 0.8);
    } catch (error) {
      console.error('Error capturing photo:', error);
    } finally {
      setIsLoading(false);
    }
  };



  if (!isVisible) return null;

  return (
    <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
      <StyledView className="flex-1 bg-black">
        {/* Header */}
        <StyledView className="flex-row items-center justify-between p-4 bg-black/80 z-10">
          <StyledTouchableOpacity onPress={onClose} className="p-2">
            <Feather name="x" size={24} color="white" />
          </StyledTouchableOpacity>
          <StyledView className="flex-1 items-center">
            <StyledText className="text-white font-medium">{title}</StyledText>
          </StyledView>
          <StyledView className="w-10" />
        </StyledView>

        {hasPermission === null && (
          <StyledView className="flex-1 items-center justify-center">
            <StyledText className="text-white">Requesting camera access...</StyledText>
          </StyledView>
        )}

        {hasPermission === false && (
          <StyledView className="flex-1 items-center justify-center p-6">
            <Feather name="camera-off" size={64} color="white" className="mb-6" />
            <StyledText className="text-white text-center mb-4 text-xl font-medium">
              Camera Access Required
            </StyledText>
            <StyledText className="text-white/70 text-center mb-8 text-base">
              This feature requires camera access to take live photos. Please allow camera access in your browser settings and try again.
            </StyledText>

            <StyledView className="w-full max-w-sm">
              <StyledTouchableOpacity
                className="bg-secondary rounded-xl py-4 px-6 items-center mb-4"
                onPress={startCamera}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="camera" size={20} color="white" />
                  <StyledText className="text-white font-medium ml-2">Try Camera Again</StyledText>
                </StyledView>
              </StyledTouchableOpacity>

              <StyledTouchableOpacity
                className="border border-white/30 rounded-xl py-4 px-6 items-center"
                onPress={onClose}
              >
                <StyledText className="text-white/70 font-medium">Cancel</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        )}

        {hasPermission === true && (
          <>
            {/* Camera View */}
            <StyledView className="flex-1 relative">
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                autoPlay
                playsInline
                muted
                style={{
                  transform: captureType === 'selfie' ? 'scaleX(-1)' : 'none',
                  backgroundColor: '#000'
                }}
              />

              {/* Capture Area Overlay */}
              <StyledView className="absolute inset-0 items-center justify-center pointer-events-none">
                {captureType === 'selfie' ? (
                  <StyledView
                    className="border-2 border-white/50 rounded-full"
                    style={{ width: 256, height: 256 }}
                  />
                ) : (
                  <StyledView
                    className="border-2 border-white/50 rounded-lg"
                    style={{ width: 320, height: 200 }}
                  />
                )}
              </StyledView>

              {/* Instructions */}
              <StyledView className="absolute bottom-32 left-0 right-0 items-center">
                <StyledView className="bg-black/70 rounded-lg px-4 py-2">
                  <StyledText className="text-white text-center text-sm">
                    {captureType === 'selfie'
                      ? 'Position your face in the circle'
                      : 'Position your document in the frame'
                    }
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Bottom Controls */}
            <StyledView className="flex-row items-center justify-center p-6 bg-black/80">
              <StyledTouchableOpacity
                onPress={capturePhoto}
                disabled={isLoading}
                className="w-20 h-20 bg-white rounded-full items-center justify-center shadow-lg"
              >
                {isLoading ? (
                  <StyledView className="w-16 h-16 bg-gray-300 rounded-full animate-pulse" />
                ) : (
                  <StyledView className="w-16 h-16 bg-white border-4 border-gray-300 rounded-full" />
                )}
              </StyledTouchableOpacity>
            </StyledView>

            {/* Hidden canvas for photo capture */}
            <canvas ref={canvasRef} className="hidden" />
          </>
        )}
      </StyledView>
    </Modal>
  );
};
