import React, { useState } from 'react';
import { Platform, Alert } from 'react-native';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';
import { CameraCapture } from './CameraCapture';
import { WebCameraCapture } from './WebCameraCapture';
import * as ImagePicker from 'expo-image-picker';

interface PhotoCaptureProps {
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
  hasPhoto: boolean;
  disabled?: boolean;
}

export const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  onCapture,
  captureType,
  title,
  subtitle,
  hasPhoto,
  disabled = false
}) => {
  const [showCamera, setShowCamera] = useState(false);

  const handleCameraCapture = (imageUri: string) => {
    onCapture(imageUri);
    setShowCamera(false);
  };

  const handleGalleryPick = async () => {
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please enable photo library access to select images.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: captureType === 'selfie' ? [1, 1] : [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onCapture(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking from gallery:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const CameraComponent = Platform.OS === 'web' ? WebCameraCapture : CameraCapture;

  return (
    <>
      <StyledView className="items-center">
        {/* Photo Icon */}
        <StyledView className="w-24 h-24 bg-gray-100 rounded-full items-center justify-center mb-6">
          <Feather 
            name={captureType === 'selfie' ? 'user' : 'file-text'} 
            size={32} 
            color="#9CA3AF" 
          />
        </StyledView>

        {/* Title and Subtitle */}
        <StyledView className="mb-8">
          <StyledText className="text-xl font-bold text-center mb-2">{title}</StyledText>
          <StyledText className="text-gray-600 text-center">{subtitle}</StyledText>
        </StyledView>

        {/* Action Buttons */}
        <StyledView className="w-full">
          {/* Primary Action - Camera */}
          <StyledTouchableOpacity
            className="bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-8 items-center mb-4 shadow-md transition-colors"
            onPress={() => setShowCamera(true)}
            disabled={disabled}
          >
            <StyledView className="flex-row items-center">
              <Feather name="camera" size={20} color="white" />
              <StyledText className="text-white font-medium ml-2">
                {hasPhoto ? 'Retake Photo' : 'Take Photo'}
              </StyledText>
            </StyledView>
          </StyledTouchableOpacity>

          {/* Secondary Action - Gallery */}
          <StyledTouchableOpacity
            className="border border-gray-300 hover:bg-gray-50 rounded-xl py-4 px-8 items-center mb-4 transition-colors"
            onPress={handleGalleryPick}
            disabled={disabled}
          >
            <StyledView className="flex-row items-center">
              <Feather name="image" size={20} color="#6B7280" />
              <StyledText className="text-gray-700 font-medium ml-2">
                Choose from Gallery
              </StyledText>
            </StyledView>
          </StyledTouchableOpacity>

          {/* Success State */}
          {hasPhoto && (
            <StyledView className="bg-green-50 border border-green-200 rounded-lg p-3">
              <StyledText className="text-green-700 text-sm text-center">
                ✓ {captureType === 'selfie' ? 'Selfie' : 'Document'} uploaded successfully
              </StyledText>
            </StyledView>
          )}
        </StyledView>
      </StyledView>

      {/* Camera Modal */}
      <CameraComponent
        isVisible={showCamera}
        onClose={() => setShowCamera(false)}
        onCapture={handleCameraCapture}
        captureType={captureType}
        title={title}
        subtitle={subtitle}
      />
    </>
  );
};
